# 项目打包教程

## 概述

UnifyPy是一个强大的自动化解决方案，能将Python项目打包成跨平台的独立可执行文件和安装程序。小智客户端已配置了相应的打包配置文件，本教程将指导您如何使用UnifyPy进行打包。

## 准备工作

### 1. 安装项目依赖

首先，确保您已安装项目的所有依赖：

```bash
# Windows
pip install -r requirements.txt

# macOS
pip install -r requirements_mac.txt

# Linux
pip install -r requirements.txt
```

### 2. 克隆UnifyPy仓库

```bash
git clone https://github.com/huangjunsen0406/UnifyPy.git
cd UnifyPy
pip install -r requirements.txt
```

### 3. 安装平台特定工具

#### Windows平台
- 安装[Inno Setup](https://jrsoftware.org/isdl.php)（用于创建安装程序）
- 安装后，在build.json中配置Inno Setup路径，或设置环境变量INNO_SETUP_PATH

#### macOS平台
- 安装create-dmg（用于创建DMG镜像）：
  ```bash
  brew install create-dmg
  ```

#### Linux平台
根据需要的打包格式，安装相应的工具：

```bash
# DEB格式(Debian/Ubuntu)
sudo apt-get install dpkg-dev

# RPM格式(Fedora/CentOS)
sudo dnf install rpm-build

# AppImage格式(通用Linux)
wget -c https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage
chmod a+x appimagetool-x86_64.AppImage
sudo mv appimagetool-x86_64.AppImage /usr/local/bin/appimagetool
```

## 打包配置详解

小智客户端已经提供了预配置的`build.json`文件，以下是各配置项的详细说明：

### 基本配置

```json
{
    "name": "xiaozhi",                  // 应用程序名称，将用于可执行文件和安装程序名称
    "version": "1.0.0",                 // 应用程序版本号
    "publisher": "Junsen",              // 发布者名称
    "entry": "main.py",                 // 程序入口文件
    "icon": "assets/xiaozhi_icon.ico",  // 应用图标路径
    "hooks": "hooks",                   // PyInstaller钩子目录
    "onefile": false,                   // 是否生成单文件模式的可执行文件
    
    // PyInstaller通用参数，适用于所有平台
    "additional_pyinstaller_args": "--add-data assets;assets --add-data libs;libs --add-data src;src --add-data models;models --hidden-import=PyQt5",
    
    // Inno Setup路径（Windows平台需要）
    "inno_setup_path": "E:\\application\\Inno Setup 6\\ISCC.exe",
    
    // 其他配置...
}
```

> **注意**：JSON文件不支持注释，上述代码中的注释仅用于说明，实际配置文件中不应包含注释。

### 平台特定配置

#### Windows平台配置

```json
"windows": {
    "format": "exe",                  // 输出格式
    "additional_pyinstaller_args": "--add-data assets;assets --add-data libs;libs --add-data src;src --add-data models;models --hidden-import=PyQt5 --noconsole",
    "desktop_entry": true,            // 是否创建桌面快捷方式
    "installer_options": {
        "languages": ["ChineseSimplified", "English"],  // 安装程序支持的语言
        "license_file": "LICENSE",                      // 许可证文件
        "readme_file": "README.md",                     // 自述文件
        "create_desktop_icon": true,                    // 是否创建桌面图标
        "allow_run_after_install": true                 // 安装后是否允许立即运行
    }
}
```

#### Linux平台配置

```json
"linux": {
    "format": "deb",                  // 输出格式，可选值：deb, rpm, appimage
    "desktop_entry": true,            // 是否创建桌面快捷方式
    "categories": "Utility;Development;",  // 应用程序类别
    "description": "小智Ai客户端",          // 应用描述
    "requires": "libc6,libgtk-3-0,libx11-6,libopenblas-dev",  // 依赖项
    "additional_pyinstaller_args": "--add-data assets:assets --add-data libs:libs --add-data src:src --add-data models:models --hidden-import=PyQt5"
}
```

#### macOS平台配置

```json
"macos": {
    "format": "app",                  // 输出格式，可选值：app, dmg
    "additional_pyinstaller_args": "--add-data assets:assets --add-data libs:libs --add-data src:src --add-data models:models --hidden-import=PyQt5 --windowed",
    "app_bundle_name": "XiaoZhi.app", // 应用包名称
    "bundle_identifier": "com.junsen.xiaozhi",  // 应用标识符
    "sign_bundle": false,             // 是否签名应用包
    "create_dmg": true,               // 是否创建DMG镜像
    "installer_options": {
        "license_file": "LICENSE",    // 许可证文件
        "readme_file": "README.md"    // 自述文件
    }
}
```

### 其他重要配置项

```json
"build_installer": true  // 是否构建安装程序，设为false只生成可执行文件
```

### 自定义安装程序模板

当打包Windows安装程序时，UnifyPy使用`setup.iss.template`文件作为Inno Setup的脚本模板。需要注意的是，模板中的AppId需要更换为自己的唯一标识符：

```
[Setup]
; 应用程序信息
AppId={{05DBB87C-AE34-4F2F-AEC5-3CD2AFE9DC90}}  ; 需要替换为你自己生成的GUID
```

> **重要**：请勿直接使用示例中的AppId，这可能导致与其他应用程序冲突。您可以使用在线GUID生成工具（如[Online GUID Generator](https://www.guidgenerator.com/)）来生成自己的唯一标识符。

## 执行打包

### 基本打包命令

```bash
# 导航到UnifyPy目录
cd 到当前py-xiaozhi项目目录下

# 执行打包命令
# UnifyPy_路径/main.py 是UnifyPy的项目路径
# . 表示当前项目目录
# --config build.json 表示使用当前目录下的build.json配置文件
python UnifyPy_路径/main.py . --config build.json 
```

### 针对不同平台的打包命令

#### Windows平台
```bash
python C:\路径\到\UnifyPy\main.py . --config build.json
```

#### macOS平台
```bash
python /路径/到/UnifyPy/main.py . --config build.json
```

#### Linux平台

Linux平台下打包有两种主要格式：DEB和AppImage。根据需要选择其中一种格式。

##### DEB格式打包（适用于Debian/Ubuntu系统）

1. **准备环境**

   ```bash
   # 更新系统并安装必要的依赖
   sudo apt update
   sudo apt install -y build-essential python3-dev python3-pip python3-setuptools libopenblas-dev liblapack-dev gfortran patchelf autoconf automake libtool cmake libssl-dev libatlas-base-dev
   ```

2. **执行打包**

   确保build.json中linux.format设置为"deb"，然后执行：

   ```bash
   python3 /路径/到/UnifyPy/main.py . --config build.json
   ```

##### AppImage格式打包（适用于通用Linux系统）

AppImage格式需要特别注意NumPy库的编译，以下是完整步骤：

1. **升级pip和基本构建工具**

   ```bash
   python -m pip install --upgrade pip setuptools wheel
   ```

2. **安装必要的系统依赖**

   ```bash
   sudo apt update
   sudo apt install -y build-essential python3-dev python3-pip python3-setuptools libopenblas-dev liblapack-dev gfortran patchelf autoconf automake libtool cmake libssl-dev libatlas-base-dev
   ```

3. **安装Meson和Ninja构建系统**

   ```bash
   pip install meson ninja
   sudo apt install -y meson ninja-build
   ```

4. **准备NumPy编译环境**

   ```bash
   # 卸载现有NumPy
   pip uninstall numpy -y
   
   # 设置环境变量
   export BLAS=openblas
   export LAPACK=openblas
   export NPY_NUM_BUILD_JOBS=$(nproc)  # 使用所有CPU核心加速编译
   
   # 从源码编译安装NumPy
   pip install numpy==1.26.4 --no-binary :all:
   ```

5. **执行打包**

   确保build.json中linux.format设置为"appimage"，然后执行：

   ```bash
   python3 /路径/到/UnifyPy/main.py . --config build.json
   ```

## 打包输出

成功打包后，将在项目根目录下的`dist`文件夹中找到打包的应用程序：

- **Windows**: 
  - 可执行文件(.exe)位于`dist/xiaozhi`目录
  - 安装程序位于`dist/installer`目录，命名为`xiaozhi-1.0.0-setup.exe`

- **macOS**: 
  - 应用程序包(.app)位于`dist/xiaozhi`目录
  - 磁盘镜像(.dmg)位于`dist/installer`目录，命名为`xiaozhi-1.0.0.dmg`

- **Linux**: 
  - 可执行文件位于`dist/xiaozhi`目录
  - 安装包位于`dist/installer`目录：
    - DEB格式：`xiaozhi-1.0.0.deb`
    - RPM格式：`xiaozhi-1.0.0.rpm`
    - AppImage格式：`xiaozhi-1.0.0.AppImage`

## 高级配置选项

### PyInstaller参数

在`additional_pyinstaller_args`字段中，您可以添加任何PyInstaller支持的参数。以下是一些常用参数：

- `--noconsole`: 不显示控制台窗口（仅适用于图形界面程序）
- `--windowed`: 等同于`--noconsole`
- `--hidden-import=MODULE`: 添加隐式导入的模块
- `--add-data SRC;DEST`: 添加数据文件（Windows平台使用分号分隔）
- `--add-data SRC:DEST`: 添加数据文件（macOS/Linux平台使用冒号分隔）
- `--icon=FILE.ico`: 设置应用程序图标

### 处理特殊依赖

某些Python库可能需要特殊处理才能正确打包，可以通过以下方式解决：

1. **使用钩子文件**：在`hooks`目录中创建自定义钩子，处理特殊导入情况
2. **添加隐式导入**：使用`--hidden-import`参数显式包含隐式导入的模块
3. **添加数据文件**：使用`--add-data`参数包含程序运行所需的数据文件

## 常见问题及解决方案

### Windows平台常见问题

1. **找不到Inno Setup**
   
   解决方案：确保已安装Inno Setup，并在build.json中正确配置路径，或设置环境变量INNO_SETUP_PATH

2. **缺少隐式导入的模块**
   
   解决方案：在`additional_pyinstaller_args`中添加`--hidden-import=模块名称`

3. **打包后无法找到资源文件**
   
   解决方案：确保使用相对路径访问资源文件，并使用`--add-data`参数正确包含资源文件

### macOS平台常见问题

1. **创建DMG出错**
   
   解决方案：确保已安装create-dmg工具，并有正确的权限

2. **签名问题**
   
   解决方案：如果需要签名，在配置文件中启用`sign_bundle`并提供有效的开发者身份

3. **动态库加载问题**
   
   解决方案：确保代码中正确处理库路径，特别是使用`sys._MEIPASS`路径

### Linux平台常见问题

1. **缺少依赖库**
   
   解决方案：在Linux配置中的`requires`字段中添加所需的系统依赖

2. **打包工具不可用**
   
   解决方案：确保已安装相应格式的打包工具(dpkg-dev, rpmbuild, appimagetool)

3. **权限问题**
   
   解决方案：确保脚本有正确的执行权限，对某些资源目录可能需要特别处理

4. **NumPy编译失败**

   解决方案：确保已安装所有必要的开发库，特别是OpenBLAS、LAPACK和Fortran编译器

5. **找不到appimagetool**

   解决方案：确保已正确安装并设置appimagetool的可执行权限

## 最佳实践

1. **清理项目**：打包前移除临时文件、缓存和不必要的大型文件
2. **测试依赖**：确保所有依赖都正确安装并可以导入
3. **确认文件路径**：检查代码中的文件路径是否使用相对路径或资源路径
4. **验证配置**：确保build.json中的配置与您的环境一致
5. **多平台测试**：如果条件允许，在多个平台上测试打包的应用程序
6. **保存配置**：为不同的打包场景保存不同版本的配置文件，方便复用
7. **版本管理**：每次发布前更新版本号，保持版本一致性