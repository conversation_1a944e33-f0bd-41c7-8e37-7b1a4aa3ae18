<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QWidget" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>518</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>卡皮巴拉式女友</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="navigation_layout">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QTabBar" name="nav_tab_bar" native="true">
       <property name="styleSheet">
        <string>
QTabBar::tab {
    background-color: #f0f0f0; /* Light gray background */
    color: #333; /* Dark text */
    padding: 8px 20px; /* Padding around text */
    border-top-left-radius: 8px; /* Rounded top corners */
    border-top-right-radius: 8px;
    border: 1px solid #ddd; /* Light border */
    margin-right: 2px; /* Space between tabs */
    min-width: 100px; /* Ensure tabs aren't too small */
}

QTabBar::tab:selected {
    background-color: #ffffff; /* White background for selected tab */
    color: #007bff; /* Blue text for selected tab */
    border: 1px solid #ccc;
    border-bottom: 2px solid #007bff; /* Blue underline for selected tab */
}

QTabBar::tab:hover {
    background-color: #e9e9e9; /* Slightly darker on hover */
}

QTabBar {
    /* Optional: Remove the default frame if needed */
    /* border: none; */
}
        </string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="mainPage">
      <property name="styleSheet">
       <string>QWidget#mainPage {
    background-color: rgba(255, 255, 255, 140); 
    border-radius: 8px;
}</string>
      </property>
      <layout class="QVBoxLayout" name="mainPageLayout">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QFrame" name="status_card">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="card_layout">
          <item>
           <widget class="QLabel" name="status_label">
            <property name="font">
             <font>
              <family>Microsoft YaHei UI</family>
              <pointsize>14</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: #2196F3;
                     padding: 10px;
                     background-color: #E3F2FD;
                     border-radius: 8px;</string>
            </property>
            <property name="text">
             <string>状态: 未连接</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="emotion_label">
            <property name="font">
             <font>
              <pointsize>24</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string>margin: 15px;</string>
            </property>
            <property name="text">
             <string>😊</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="tts_text_label">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>待命</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="wordWrap">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="mic_visualizer_card">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>70</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>72</height>
          </size>
         </property>
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="mic_visualizer_layout">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>10</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>10</number>
          </property>
          <property name="bottomMargin">
           <number>5</number>
          </property>
          <item>
           <widget class="QStackedWidget" name="audio_control_stack">
            <property name="currentIndex">
             <number>0</number>
            </property>
            <widget class="QWidget" name="volume_page">
             <layout class="QHBoxLayout" name="volume_layout">
              <property name="spacing">
               <number>5</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QPushButton" name="mute">
                <property name="minimumSize">
                 <size>
                  <width>72</width>
                  <height>34</height>
                 </size>
                </property>
                <property name="text">
                 <string>点击静音</string>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QSlider" name="volume_scale">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="mic_page">
             <layout class="QVBoxLayout" name="mic_page_layout">
              <property name="spacing">
               <number>0</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QWidget" name="mic_visualizer_widget" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">border-radius: 8px;</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="button_layout">
         <property name="spacing">
          <number>8</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>6</number>
         </property>
         <item>
          <widget class="QPushButton" name="manual_btn">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>36</height>
            </size>
           </property>
           <property name="text">
            <string>按住后说话</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="abort_btn">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>36</height>
            </size>
           </property>
           <property name="text">
            <string>打断对话</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="text_input_layout">
           <property name="spacing">
            <number>4</number>
           </property>
           <item>
            <widget class="QLineEdit" name="text_input">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>36</height>
              </size>
             </property>
             <property name="placeholderText">
              <string>输入文字...</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="send_btn">
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>36</height>
              </size>
             </property>
             <property name="text">
              <string>发送</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPushButton" name="auto_btn">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>36</height>
            </size>
           </property>
           <property name="text">
            <string>开始对话</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="mode_btn">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>36</height>
            </size>
           </property>
           <property name="text">
            <string>手动对话</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="iotPage">
      <layout class="QVBoxLayout" name="historyPageLayout">
       <item>
        <widget class="QFrame" name="history_card">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="history_layout">
          <item>
           <widget class="QLabel" name="history_title">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>暂无IOT设备
暂未实现
             </string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="settingPage">
      <layout class="QVBoxLayout" name="settingPageLayout">
       <item>
        <widget class="QFrame" name="setting_card">
         <property name="frameShape">
          <enum>QFrame::StyledPanel</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="setting_layout">
          <property name="spacing">
           <number>5</number>
          </property>
          <property name="leftMargin">
           <number>16</number>
          </property>
          <property name="topMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>16</number>
          </property>
          <property name="bottomMargin">
           <number>10</number>
          </property>
          <item>
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="setting_title">
            <property name="font">
             <font>
              <pointsize>18</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>config.json文件配置</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="bottomMargin" stdset="0">
             <number>10</number>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Expanding</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <layout class="QHBoxLayout" name="wakeWordEnableLayout">
            <item>
             <widget class="QLabel" name="wakeWordEnableLabel">
              <property name="text">
               <string>启用唤醒词唤醒:</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="wakeWordEnableSwitch">
              <property name="minimumSize">
               <size>
                <width>20</width>
                <height>20</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_WakeEnable">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="wakeWordsEditLayout">
            <property name="spacing">
             <number>10</number>
            </property>
            <property name="topMargin">
             <number>10</number>
            </property>
            <item>
             <widget class="QLabel" name="wakeWordsLabel">
              <property name="text">
               <string>唤醒词 (多个唤醒词请用英文逗号 ',' 分隔):</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="wakeWordsLineEdit"/>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="label">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="text">
             <string>API 服务配置</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QFormLayout" name="formLayout">
            <property name="horizontalSpacing">
             <number>6</number>
            </property>
            <property name="verticalSpacing">
             <number>12</number>
            </property>
            <item row="0" column="0">
             <widget class="QLabel" name="deviceIdLabel">
              <property name="text">
               <string>Device ID:</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="deviceIdLineEdit"/>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="otaUrlLabel">
              <property name="text">
               <string>OTA地址:</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QWidget" name="otaUrlContainerWidget" native="true">
              <layout class="QHBoxLayout" name="otaUrlInnerLayout">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QComboBox" name="otaProtocolComboBox">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <item>
                  <property name="text">
                   <string>https://</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>http://</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="otaAddressLineEdit">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                   <horstretch>1</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="websocketUrlLabel">
              <property name="text">
               <string>API接口地址:</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QWidget" name="wsUrlContainerWidget" native="true">
              <layout class="QHBoxLayout" name="wsUrlInnerLayout">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QComboBox" name="wsProtocolComboBox">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <item>
                  <property name="text">
                   <string>wss://</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>ws://</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="wsAddressLineEdit">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                   <horstretch>1</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="websocketTokenLabel">
              <property name="text">
               <string>Access Token:</string>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QLineEdit" name="wsTokenLineEdit"/>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_Group2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Fixed</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>15</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="haConfigLabel">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Home Assistant 服务配置</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QGridLayout" name="gridLayout_2">
            <item row="0" column="0">
             <widget class="QLabel" name="haServerLabel">
              <property name="text">
               <string>HA-Server:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QWidget" name="haUrlContainerWidget" native="true">
              <layout class="QHBoxLayout" name="haUrlInnerLayout">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QComboBox" name="haProtocolComboBox">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <item>
                  <property name="text">
                   <string>https://</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>http://</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="ha_server">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                   <horstretch>1</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="placeholderText">
                  <string>Home Assistant服务地址，例如：ha.aslant.top</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="haPortLabel">
              <property name="text">
               <string>Port:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="ha_port">
              <property name="placeholderText">
               <string>默认端口：8123</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="haKeyLabel">
              <property name="text">
               <string>长期访问令牌:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="ha_key">
              <property name="echoMode">
               <enum>QLineEdit::Password</enum>
              </property>
              <property name="placeholderText">
               <string>获取方法：账户 - 安全 - 创建令牌</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QPushButton" name="Add_ha_devices">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>30</height>
             </size>
            </property>
            <property name="text">
             <string>导入Home Assistant设备</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="saveSettingsButton">
            <property name="text">
             <string>保存设置</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTabBar</class>
   <extends>QWidget</extends>
   <header>qtabbar.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
