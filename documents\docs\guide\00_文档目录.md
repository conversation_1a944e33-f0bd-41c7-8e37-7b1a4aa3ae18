# py-xiaozhi文档目录

本目录包含了 py-xiaozhi 项目 的全部功能文档，按功能模块进行划分，便于查阅和使用。

项目默认启用了以下模块：音乐、灯光、音量、定时器、Home asssistant控制器、相机 IoT 控制。
其他模块可根据需求自行扩展与启用。
视觉识别功能 需配置 智普大模型的 API Key 才可使用。
当前项目已经启用v2授权协议，wss链接通过ota接口返回、无论是官方小智和开源后端已经对齐认证流程

## 基础文档

- [01_系统依赖安装](01_系统依赖安装) - 各平台的系统依赖和Python环境配置
- [02_配置说明](02_配置说明.md) - 配置文件结构、配置项说明和修改指南
- [03_语音交互模式说明](03_语音交互模式说明) - 项目概述、基本使用说明和运行模式
- [04_语音唤醒](04_语音唤醒.md) - 语音唤醒功能的配置和使用说明
- [05_IoT功能说明](05_IoT功能说明.md) - 物联网功能架构、设备控制和扩展方法
- [06_音量控制功能](06_音量控制功能.md) - 系统音量控制功能的使用和配置
- [07_视觉识别功能](07_视觉识别功能.md) - 摄像头控制和视觉分析功能
- [08_设备激活流程](08_设备激活流程) - 设备激活和注册流程说明
- [09_打包教程](09_打包教程.md) - 使用UnifyPy打包小智客户端的详细教程
- [TTS功能说明](TTS功能说明.md) - 文本转语音功能说明

## 其他文档

- [异常汇总](异常汇总.md) - 常见问题和解决方案

## 旧版文档

为了便于参考，我们保留了旧版文档：

- [旧版使用文档](old_docs/使用文档.md) - 较早版本的使用说明文档

## 参与贡献

如果您想参与项目开发或提供反馈，请查看以下资源：

- [贡献指南](/contributing) - 如何为项目贡献代码，包括开发流程、代码规范和PR提交流程
- [团队成员](/about/team) - 感谢为项目做出贡献的团队成员
- [赞助支持](/sponsors/) - 如何赞助项目发展

## 相关生态

- [相关生态](/ecosystem/) - py-xiaozhi项目的相关生态系统和扩展项目


## 版本信息

文档最后更新时间：2025年5月 