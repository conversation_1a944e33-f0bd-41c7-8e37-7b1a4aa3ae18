#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import subprocess
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_with_desktop_pet():
    """测试主程序启动桌面宠物模式"""
    print("=== 测试主程序桌面宠物模式 ===")
    
    # 确保桌面宠物模式已启用
    try:
        from src.utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        pet_config = config.get("DESKTOP_PET", {})
        enabled = pet_config.get("enabled", False)
        
        print(f"桌面宠物模式状态: {'✅ 已启用' if enabled else '❌ 已禁用'}")
        
        if not enabled:
            print("正在启用桌面宠物模式...")
            config.setdefault("DESKTOP_PET", {})["enabled"] = True
            config_manager.save_config(config)
            print("✅ 桌面宠物模式已启用")
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False
    
    # 启动主程序
    print("\n正在启动主程序...")
    print("命令: python main.py --mode gui")
    print("注意: 如果看到桌面宠物出现，说明修改成功！")
    print("可以通过右键菜单退出程序")
    
    try:
        # 使用subprocess启动主程序
        process = subprocess.Popen([
            sys.executable, "main.py", "--mode", "gui"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("程序已启动，PID:", process.pid)
        print("等待5秒查看输出...")
        
        # 等待一段时间查看输出
        try:
            stdout, stderr = process.communicate(timeout=5)
            print("标准输出:", stdout)
            if stderr:
                print("错误输出:", stderr)
        except subprocess.TimeoutExpired:
            print("程序仍在运行中...")
            print("如果桌面宠物已显示，说明修改成功！")
            print("请手动关闭程序")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=3)
            except subprocess.TimeoutExpired:
                process.kill()
        
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    test_main_with_desktop_pet() 