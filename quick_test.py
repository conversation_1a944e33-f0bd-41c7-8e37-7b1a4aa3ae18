#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import logging

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_animation_loading():
    """测试动画加载"""
    print("=== 测试动画加载 ===")
    
    try:
        # 模拟DesktopPetDisplay的动画加载过程
        from src.display.desktop_pet_display import DesktopPetDisplay
        
        # 创建一个最小化的测试实例
        import unittest.mock
        
        # Mock QWidget和QTimer以避免GUI依赖
        with unittest.mock.patch('PyQt5.QtWidgets.QWidget.__init__'):
            with unittest.mock.patch('PyQt5.QtCore.QTimer'):
                with unittest.mock.patch('PyQt5.QtWidgets.QApplication.desktop'):
                    # 创建实例
                    pet = DesktopPetDisplay.__new__(DesktopPetDisplay)
                    
                    # 初始化必要的属性
                    pet.logger = logging.getLogger("DesktopPetDisplay")
                    pet.animations = {}
                    pet.config_manager = None
                    
                    # 测试load_animations方法
                    print("正在调用load_animations...")
                    pet.load_animations()
                    
                    print(f"加载的动画数量: {len(pet.animations)}")
                    for state, path in pet.animations.items():
                        print(f"  {state}: {path}")
                    
                    # 测试main.gif是否存在
                    if "main" in pet.animations:
                        main_path = pet.animations["main"]
                        if os.path.exists(main_path):
                            print(f"✅ main.gif 存在且可访问: {main_path}")
                        else:
                            print(f"❌ main.gif 路径无效: {main_path}")
                    else:
                        print("❌ main.gif 未加载")
                        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_animation_loading() 