#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import logging

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_path_calculation():
    """测试路径计算"""
    print("=== 测试路径计算 ===")
    
    # 模拟desktop_pet_display.py中的路径计算
    print(f"当前脚本文件: {__file__}")
    print(f"当前脚本绝对路径: {os.path.abspath(__file__)}")
    
    # 模拟从src/display/desktop_pet_display.py计算项目根目录
    simulated_file = os.path.join(os.getcwd(), "src", "display", "desktop_pet_display.py")
    print(f"模拟的desktop_pet_display.py路径: {simulated_file}")
    
    if getattr(sys, 'frozen', False):
        # 打包环境
        if hasattr(sys, '_MEIPASS'):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.dirname(sys.executable)
    else:
        # 开发环境 - 从当前文件位置计算项目根目录
        # src/display/desktop_pet_display.py -> 项目根目录
        current_file = os.path.abspath(simulated_file)
        base_path = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
    
    print(f"计算出的项目根目录: {base_path}")
    
    character_path = os.path.join(base_path, "assets", "characters", "default")
    print(f"动画文件搜索路径: {character_path}")
    
    # 检查动画文件
    animation_states = ["main", "idle", "listening", "speaking", "thinking"]
    
    for state in animation_states:
        animation_file = os.path.join(character_path, f"{state}.gif")
        exists = os.path.exists(animation_file)
        print(f"{state}.gif: {animation_file} - {'存在' if exists else '不存在'}")

def test_direct_import():
    """测试直接导入DesktopPetDisplay"""
    print("\n=== 测试直接导入 ===")
    
    try:
        from src.display.desktop_pet_display import DesktopPetDisplay
        
        # 创建实例但不启动UI
        print("正在创建DesktopPetDisplay实例...")
        
        # 暂时禁用QApplication相关的初始化
        import unittest.mock
        with unittest.mock.patch('PyQt5.QtWidgets.QWidget.__init__'):
            with unittest.mock.patch('PyQt5.QtCore.QTimer'):
                pet = DesktopPetDisplay.__new__(DesktopPetDisplay)
                pet.logger = logging.getLogger("DesktopPetDisplay")
                pet.animations = {}
                
                # 直接调用load_animations方法
                pet.load_animations()
                
                print(f"加载的动画: {pet.animations}")
                
    except Exception as e:
        print(f"导入失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_path_calculation()
    test_direct_import() 