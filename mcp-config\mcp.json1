{"mcpServers": {"everything-search": {"isActive": true, "name": "everything-search", "type": "stdio", "registryUrl": "", "command": "uvx", "args": ["mcp-server-everything-search"], "env": {"EVERYTHING_SDK_PATH": "D:\\everything\\Everything64.dll"}}, "fetcher-mcp": {"isActive": true, "name": "fetcher-mcp", "type": "stdio", "description": "", "registryUrl": "", "tags": [], "command": "npx", "args": ["-y", "fetcher-mcp"]}, "MCP-timeserver": {"isActive": true, "name": "MCP-timeserver", "type": "stdio", "description": "", "registryUrl": "https://pypi.tuna.tsinghua.edu.cn/simple", "command": "uvx", "args": ["MCP-timeserver"]}, "edgeone-pages": {"isActive": true, "name": "edgeone-pages-mcp-server", "type": "stdio", "description": "", "registryUrl": "https://registry.npmmirror.com", "command": "npx", "args": ["edgeone-pages-mcp"]}, "firecrawl": {"isActive": true, "name": "firecrawl-mcp", "type": "stdio", "description": "", "registryUrl": "", "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-7e3a530a6a1646c988934431decf3382"}}, "desktop-commander": {"isActive": true, "name": "desktop-commander", "type": "stdio", "description": "", "registryUrl": "", "command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "amap": {"isActive": true, "name": "高德地图", "type": "sse", "description": "", "baseUrl": "https://mcp.amap.com/sse?key=5d6ce2a24d3bccde9e68a73d16ca210e"}, "mcp-auto-install": {"isActive": true, "name": "mcp-auto-install", "description": "Automatically install MCP services (Beta version)", "baseUrl": "", "command": "npx", "args": ["-y", "@mcpmarket/mcp-auto-install", "connect", "--json"], "registryUrl": "https://registry.npmmirror.com", "env": {}}, "grok": {"name": "grok2-imag", "type": "stdio", "description": "", "isActive": true, "registryUrl": "", "command": "npx", "args": ["grok2-image-mcp-server"], "env": {"XAIAPI_KEY": "************************************************************************************"}}, "fetch": {"isActive": false, "name": "fetch", "command": "uvx", "args": ["mcp-server-fetch"]}}}