#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面宠物测试脚本
用于测试桌面宠物显示功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigManager
import logging

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_desktop_pet():
    """测试桌面宠物功能"""
    print("开始测试桌面宠物功能...")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    try:
        # 创建桌面宠物显示
        pet_display = DesktopPetDisplay()
        
        # 设置测试回调函数
        def test_callback(action):
            print(f"回调触发: {action}")
        
        pet_display.set_callbacks(
            press_callback=lambda: test_callback("按下"),
            release_callback=lambda: test_callback("释放"),
            status_callback=lambda: "测试状态",
            text_callback=lambda: "测试文本",
            emotion_callback=lambda: "neutral",
            mode_callback=lambda mode: test_callback(f"模式切换: {mode}"),
            auto_callback=lambda: test_callback("自动模式"),
            abort_callback=lambda: test_callback("中止"),
            send_text_callback=lambda text: test_callback(f"发送文本: {text}")
        )
        
        # 启动显示
        pet_display.start()
        
        # 测试状态更新
        pet_display.update_status("测试状态")
        pet_display.update_text("这是一个测试消息")
        pet_display.update_emotion("happy")
        
        print("桌面宠物已启动，右键菜单可以退出")
        print("双击宠物可以打开设置")
        print("拖拽宠物可以移动位置")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def enable_desktop_pet_mode():
    """启用桌面宠物模式"""
    try:
        config_manager = ConfigManager.get_instance()
        config_manager.update_config("DESKTOP_PET.enabled", True)
        print("桌面宠物模式已启用")
        return True
    except Exception as e:
        print(f"启用桌面宠物模式失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 桌面宠物测试工具 ===")
    print("1. 测试桌面宠物显示")
    print("2. 启用桌面宠物模式")
    print("3. 退出")
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == "1":
        test_desktop_pet()
    elif choice == "2":
        if enable_desktop_pet_mode():
            print("配置已更新，重启应用程序后生效")
        else:
            print("配置更新失败")
    elif choice == "3":
        print("退出")
    else:
        print("无效选择") 