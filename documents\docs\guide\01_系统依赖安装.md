# 系统依赖安装
⚠️务必按照教程安装顺序进行软件、工具的安装

⚠️推荐使用conda环境进行安装，PyQt5、OpenCV可以直接使用conda预编译好的版本。pip在arm64 4GB及其以下设备上PyQt5、OpenCV极其容易编译失败无法安装


## 系统依赖安装
### 多媒体处理组件
#### 1. **FFmpeg 视频处理工具**

##### 📦 Windows 安装方式

* **推荐方式：使用 Scoop 安装**

  ```bash
  scoop install ffmpeg
  ```

* **手动安装**

  1. 前往 [BtbN/FFmpeg-Builds](https://github.com/BtbN/FFmpeg-Builds/releases) 下载对应版本
  2. 解压后将 `bin` 目录路径添加至系统环境变量 `PATH`

##### 🌐 跨平台安装（适用于 Conda 环境）

```bash
conda install -c conda-forge ffmpeg
```

---

#### 2. **Opus 音频编解码器**

* 默认情况下，项目已自动引入 `opus.dll`，**通常无需额外安装**

* 如遇找不到库的问题，Windows 用户可手动复制 `/libs/windows/opus.dll` 到以下任一位置：

  * 应用程序运行目录
  * `C:\Windows\System32`

##### Conda 环境安装方式：

```bash
conda install -c conda-forge opus
```

### Linux (Debian/Ubuntu)
```bash
# 安装系统依赖
sudo apt-get update
# 必装
sudo apt-get install python3-pyaudio portaudio19-dev ffmpeg libopus0 libopus-dev build-essential  python3-venv

# 安装音量控制依赖（以下三选一）
# 1. PulseAudio 工具（推荐）
sudo apt-get install pulseaudio-utils

# 2. 或者 ALSA 工具
sudo apt-get install alsa-utils

# 3. 如果需要使用 alsamixer 方式，还需要安装 expect
sudo apt-get install alsa-utils expect
```

### macOS
```bash
# 使用 Homebrew 安装系统依赖
brew install portaudio opus python-tk ffmpeg gfortran
brew upgrade tcl-tk
```

## Python 依赖安装

---

### 方式一：使用 Miniconda（推荐）

### 1. 下载 Miniconda 安装包（
根据你的系统架构或操作系统选择下载命令：

| 系统 / 架构 | 下载指令 |
|:-----------|:---------|
| **Linux - x86_64**（PC/服务器常用） | ```bash wget -O Miniconda3-latest-Linux-x86_64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh ``` |
| **Linux - aarch64**（ARM64，比如树莓派、部分服务器） | ```bash wget -O Miniconda3-latest-Linux-aarch64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-aarch64.sh ``` |
| **Linux - ppc64le**（IBM Power服务器） | ```bash wget -O Miniconda3-latest-Linux-ppc64le.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-ppc64le.sh ``` |
| **Windows - x86_64**（普通Windows PC） | [点击下载](https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe) |
| **Windows - arm64**（ARM Windows设备，如Surface Pro X） | [点击下载](https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-arm64.exe) |
| **macOS - x86_64**（Intel芯片Mac） | ```bash wget -O Miniconda3-latest-MacOSX-x86_64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh ``` |
| **macOS - arm64**（Apple Silicon芯片，如M1/M2/M3） | ```bash wget -O Miniconda3-latest-MacOSX-arm64.sh https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-arm64.sh ``` |

---

### 2. 给安装脚本添加执行权限（仅Linux/macOS）

```bash
chmod +x Miniconda3-latest-*.sh
```

### 3. 运行安装程序（仅Linux/macOS）

```bash
./Miniconda3-latest-*.sh
```
⚡ *注意：Linux/macOS下不需要用`sudo`，用普通用户安装即可。*

---

### 4. 安装过程中 （windows请按照网络搜索到的教程来）

1. 出现许可协议 → 按 `Enter` 键慢慢翻，或按 `q` 直接跳过。
2. 输入 `yes` 接受协议。
3. 选择安装路径，默认是 `$HOME/miniconda3` → 直接按 `Enter` 确认。
4. 是否初始化 Miniconda → 输入 `yes`（推荐）。

---

### 5. 配置环境变量（如果未自动配置）

Linux/macOS 编辑 `.bashrc`：

```bash
nano ~/.bashrc
```

在文件末尾添加：

```bash
export PATH="$HOME/miniconda3/bin:$PATH"
```

保存并退出：
- 按 `Ctrl + X`
- 按 `Y`
- 按 `Enter`

让配置立即生效：

```bash
source ~/.bashrc
```

---

### 6. 检查 conda 安装成功

```bash
conda --version
```
如果看到版本号，比如 `conda 24.1.2`，就代表安装成功！

---

### 7. 初始化conda（可选但推荐）

```bash
conda init
bash
```

然后打开新终端，看到 `(base)`，说明环境正常激活。

---

### 8. （推荐）关闭开机自动激活 base 环境

```bash
conda config --set auto_activate_base false
```

这样以后打开终端是干净的，需要的时候再手动 `conda activate base`。

### 一键自动更换当前网络下最快pip软件源

* **为了更稳定更快速的安装所需依赖包，建议安装**

[工具地址|chsrc 全平台通用换源工具|GitHub仓库](https://github.com/RubyMetric/chsrc)

```bash
# windows安装（PowerShell管理员权限打开输入下面这句）
winget install RubyMetric.chsrc --source winget

# 个人服务器，加速下载，支持x86_64和arm64架构
wget -O- aslant.top/chsrc.sh|sudo bash

# 更换pip软件源
chsrc set pip
```

## 安装项目依赖

### 1. 创建 Conda 环境
```bash
conda create -n py-xiaozhi python=3.10 -y
```

### 2. 激活环境
```bash
conda activate py-xiaozhi
```

### 3. 安装 Python 依赖
```bash
# Windows/Linux
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple
# macOS
pip install -r requirements_mac.txt -i https://mirrors.aliyun.com/pypi/simple
```

* 安装其它依赖(**因为pip安装这两个可能会启动不了。需要在conda单独安装**)
```bash
# 依然在创建的px-xiaozhi虚拟环境中
# PyQt5
conda install pyqt=5.15.10 -y

# OpenCV
conda install opencv=4.10.0 -y

# windows电脑需要单独安装这个
pip install wmi
```


---

### 方式二：使用 venv (不再推荐)
- mac上会提示系统无法验证 SSL 证书的签发机构，也就是说缺少或未安装系统根证书（CA certificates）
```bash
# 1. 创建虚拟环境
python -m venv .venv

# 2. 激活虚拟环境
# Windows
.venv\Scripts\activate
# Linux/macOS
source .venv/bin/activate

# 3. 安装依赖
# Windows/Linux
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple
# macOS
pip install -r requirements_mac.txt -i https://mirrors.aliyun.com/pypi/simple

# 由于requirements 去除了这几个默认的需要单独安装
pip install PyQt5==5.15.9 opencv-python==********* wmi==1.5.1 -i https://mirrors.aliyun.com/pypi/simple
```

## 注意事项
1. 建议使用 Python 3.9.13+ 版本，推荐 3.10 最大版本3.12（不行就降级）
2. Windows 用户无需手动安装 opus.dll，项目会自动处理
3. 使用 Conda 环境时必须安装 ffmpeg 和 Opus
4. 使用 Conda 环境时请勿和esp32-server共用同一个Conda环境，因为服务端websocket依赖版本高于本项目
5. 建议使用国内镜像源安装依赖，可以提高下载速度
6. macOS 用户需使用专门的 requirements_mac.txt
7. 确保系统依赖安装完成后再安装 Python 依赖