import sys
import os
import logging
from typing import Optional, Callable
from PyQt5.QtWidgets import (QWidget, QApplication, QLabel, QVBoxLayout, 
                             QMenu, QAction)
from PyQt5.QtCore import Qt, QTimer, QPoint
from PyQt5.QtGui import QMovie, QFont, QCursor

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config_manager import ConfigManager

class DesktopPetDisplay(QWidget):
    """桌面宠物显示类"""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化BaseDisplay的功能
        self.current_volume = 70
        self.volume_controller = None
        self._init_volume_controller()
        
        # 回调函数
        self.press_callback = None
        self.release_callback = None
        self.status_callback = None
        self.text_callback = None
        self.emotion_callback = None
        self.mode_callback = None
        self.auto_callback = None
        self.abort_callback = None
        self.send_text_callback = None
        
        # 配置管理器
        self.config_manager = ConfigManager()
        
        # 动画相关
        self.current_animation = None
        self.animation_label = None
        self.status_label = None
        
        # 窗口状态
        self.is_dragging = False
        self.drag_position = QPoint()
        
        # 动画状态
        self.current_state = "idle"
        self.animations = {}
        
        # 初始化UI
        self.setup_ui()
        self.load_animations()  # 先加载动画文件
        self.load_position()
        
        # 设置默认动画（在动画加载完成后）
        self.set_character_animation("idle")
        
        # 定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.process_updates)
        self.update_timer.start(100)  # 100ms更新一次
        
        self.logger.info("桌面宠物显示初始化完成")

    def _init_volume_controller(self):
        """初始化音量控制器"""
        try:
            from src.utils.volume_controller import VolumeController
            if VolumeController.check_dependencies():
                self.volume_controller = VolumeController()
                self.logger.info("音量控制器初始化成功")
                try:
                    self.current_volume = self.volume_controller.get_volume()
                    self.logger.info(f"读取到系统音量: {self.current_volume}%")
                except Exception as e:
                    self.logger.warning(f"获取初始系统音量失败: {e}，将使用默认值 {self.current_volume}%")
            else:
                self.logger.warning("音量控制依赖不满足，将使用默认音量控制")
        except Exception as e:
            self.logger.warning(f"音量控制器初始化失败: {e}，将使用模拟音量控制")

    def setup_ui(self):
        """设置UI布局"""
        # 窗口设置
        self.setWindowFlags(
            Qt.FramelessWindowHint |  # 无边框
            Qt.WindowStaysOnTopHint |  # 置顶
            Qt.Tool  # 不在任务栏显示
        )
        self.setAttribute(Qt.WA_TranslucentBackground)  # 透明背景
        self.setFixedSize(200, 300)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 角色显示标签
        self.animation_label = QLabel()
        self.animation_label.setAlignment(Qt.AlignCenter)
        self.animation_label.setStyleSheet("background: transparent;")
        self.animation_label.setScaledContents(True)
        layout.addWidget(self.animation_label)
        
        # 状态气泡（初始隐藏）
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 255, 255, 200);
                border: 2px solid rgba(100, 150, 255, 150);
                border-radius: 15px;
                padding: 8px 12px;
                color: #333;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        self.status_label.hide()
        layout.addWidget(self.status_label)

    def load_animations(self):
        """加载所有动画"""
        # 获取项目根目录的绝对路径
        if getattr(sys, 'frozen', False):
            # 打包环境
            if hasattr(sys, '_MEIPASS'):
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(sys.executable)
        else:
            # 开发环境 - 从当前文件位置计算项目根目录
            # src/display/desktop_pet_display.py -> 项目根目录
            current_file = os.path.abspath(__file__)
            base_path = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        
        character_path = os.path.join(base_path, "assets", "characters", "default")
        # 将main添加到动画状态列表的开头，作为主要动画
        animation_states = ["main", "idle", "listening", "speaking", "thinking"]
        
        self.logger.info(f"当前文件: {os.path.abspath(__file__)}")
        self.logger.info(f"项目根目录: {base_path}")
        self.logger.info(f"动画文件搜索路径: {character_path}")
        
        for state in animation_states:
            animation_file = os.path.join(character_path, f"{state}.gif")
            if os.path.exists(animation_file):
                self.animations[state] = animation_file
                self.logger.info(f"✅ 成功加载动画: {state} -> {animation_file}")
            else:
                self.logger.warning(f"❌ 动画文件不存在: {animation_file}")
        
        # 特别记录main.gif的加载状态
        if "main" in self.animations:
            self.logger.info(f"🎯 成功加载主要动画文件: {self.animations['main']}")
        else:
            self.logger.warning("⚠️ 未找到main.gif，将使用其他动画作为备选")

    def set_character_animation(self, state):
        """设置角色动画状态"""
        self.current_state = state
        
        # 获取动画路径
        animation_path = self.animations.get(state)
        if not animation_path or not os.path.exists(animation_path):
            self.logger.warning(f"动画文件不存在: {state}")
            self.set_fallback_display(state)
            return
        
        try:
            # 停止当前动画
            if self.current_animation:
                self.current_animation.stop()
            
            # 创建新动画
            self.current_animation = QMovie(animation_path)
            if not self.current_animation.isValid():
                self.logger.warning(f"无效的动画文件: {animation_path}")
                self.set_fallback_display(state)
                return
            
            # 设置动画
            self.current_animation.setCacheMode(QMovie.CacheAll)
            self.animation_label.setMovie(self.current_animation)
            self.current_animation.start()
            
            self.logger.debug(f"加载动画: {animation_path}")
            
        except Exception as e:
            self.logger.error(f"设置动画失败: {e}")
            self.set_fallback_display(state)

    def set_fallback_display(self, state):
        """设置备用显示"""
        fallback_text = {
            "idle": "😊",
            "listening": "👂",
            "speaking": "💬",
            "thinking": "🤔"
        }
        
        text = fallback_text.get(state, "😊")
        self.animation_label.setText(text)
        self.animation_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #4A90E2;
                background: transparent;
            }
        """)

    def load_position(self):
        """加载窗口位置"""
        try:
            config = self.config_manager.get_full_config()
            
            pet_config = config.get("DESKTOP_PET", {})
            position = pet_config.get("position", {})
            
            x = position.get("x", -1)
            y = position.get("y", -1)
            
            if x >= 0 and y >= 0:
                self.move(x, y)
            else:
                # 默认位置：屏幕右下角
                screen = QApplication.desktop().screenGeometry()
                self.move(screen.width() - self.width() - 50, 
                         screen.height() - self.height() - 100)
                         
        except Exception as e:
            self.logger.warning(f"加载位置失败: {e}")
            # 默认位置：屏幕右下角
            screen = QApplication.desktop().screenGeometry()
            self.move(screen.width() - self.width() - 50, 
                     screen.height() - self.height() - 100)

    def save_position(self):
        """保存窗口位置"""
        try:
            # 获取完整配置
            config = self.config_manager.get_full_config()
            
            if "DESKTOP_PET" not in config:
                config["DESKTOP_PET"] = {}
            
            config["DESKTOP_PET"]["position"] = {
                "x": self.x(),
                "y": self.y()
            }
            
            # 保存完整配置
            self.config_manager.save_config(config)
            
        except Exception as e:
            self.logger.warning(f"保存位置失败: {e}")

    def show_status_bubble(self, text, duration=3000):
        """显示状态气泡"""
        if not text:
            return
            
        self.status_label.setText(text)
        self.status_label.show()
        
        # 设置自动隐藏定时器
        QTimer.singleShot(duration, self.status_label.hide)

    def process_updates(self):
        """处理更新队列"""
        try:
            # 这里可以添加定期更新的逻辑
            pass
        except Exception as e:
            self.logger.error(f"处理更新失败: {e}")

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = True
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPos())

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self.is_dragging:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.save_position()  # 保存新位置
            event.accept()

    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        
        # 设置菜单
        settings_action = QAction("打开设置", self)
        settings_action.triggered.connect(self.open_settings)
        menu.addAction(settings_action)
        
        # 分隔线
        menu.addSeparator()
        
        # 切换到传统界面
        switch_action = QAction("切换到传统界面", self)
        switch_action.triggered.connect(self.switch_to_gui)
        menu.addAction(switch_action)
        
        # 分隔线
        menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.quit_application)
        menu.addAction(exit_action)
        
        menu.exec_(position)

    def open_settings(self):
        """打开设置"""
        self.logger.info("打开设置")
        # 这里可以添加打开设置窗口的逻辑

    def switch_to_gui(self):
        """切换到传统界面"""
        self.logger.info("切换到传统界面")
        # 这里可以添加切换界面的逻辑

    def quit_application(self):
        """退出应用"""
        self.logger.info("退出应用")
        QApplication.quit()

    # 实现BaseDisplay接口
    def set_callbacks(self,
                     press_callback: Optional[Callable] = None,
                     release_callback: Optional[Callable] = None,
                     status_callback: Optional[Callable] = None,
                     text_callback: Optional[Callable] = None,
                     emotion_callback: Optional[Callable] = None,
                     mode_callback: Optional[Callable] = None,
                     auto_callback: Optional[Callable] = None,
                     abort_callback: Optional[Callable] = None,
                     send_text_callback: Optional[Callable] = None):
        """设置回调函数"""
        self.press_callback = press_callback
        self.release_callback = release_callback
        self.status_callback = status_callback
        self.text_callback = text_callback
        self.emotion_callback = emotion_callback
        self.mode_callback = mode_callback
        self.auto_callback = auto_callback
        self.abort_callback = abort_callback
        self.send_text_callback = send_text_callback

    def update_button_status(self, text: str):
        """更新按钮状态"""
        self.show_status_bubble(f"按钮状态: {text}")

    def update_status(self, status: str):
        """更新状态文本"""
        self.show_status_bubble(status)

    def update_text(self, text: str):
        """更新TTS文本"""
        self.show_status_bubble(f"TTS: {text}")

    def update_emotion(self, emotion: str):
        """更新表情"""
        self.set_character_animation(emotion)

    def get_current_volume(self):
        """获取当前音量"""
        if self.volume_controller:
            try:
                self.current_volume = self.volume_controller.get_volume()
            except Exception as e:
                self.logger.debug(f"获取系统音量失败: {e}")
        return self.current_volume

    def update_volume(self, volume: int):
        """更新系统音量"""
        volume = max(0, min(100, volume))
        self.current_volume = volume
        self.logger.info(f"设置音量: {volume}%")
        
        if self.volume_controller:
            try:
                self.volume_controller.set_volume(volume)
                self.logger.debug(f"系统音量已设置为: {volume}%")
            except Exception as e:
                self.logger.warning(f"设置系统音量失败: {e}")

    def start(self):
        """启动显示"""
        self.show()
        self.logger.info("桌面宠物显示已启动")

    def on_close(self):
        """关闭显示"""
        if self.update_timer:
            self.update_timer.stop()
        
        if self.current_animation:
            self.current_animation.stop()
        
        self.save_position()
        
        self.logger.info("桌面宠物显示已关闭")

    def start_keyboard_listener(self):
        """启动键盘监听"""
        # 桌面宠物模式下可以选择不启用键盘监听
        pass

    def stop_keyboard_listener(self):
        """停止键盘监听"""
        pass 