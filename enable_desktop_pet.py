#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import ConfigManager

def enable_desktop_pet():
    """启用桌面宠物模式"""
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 启用桌面宠物模式
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        
        # 保存配置
        config_manager.save_config(config)
        
        print("✅ 桌面宠物模式已启用！")
        print("现在可以使用以下命令启动应用：")
        print("python main.py --mode gui")
        
    except Exception as e:
        print(f"❌ 启用失败: {e}")
        import traceback
        traceback.print_exc()

def disable_desktop_pet():
    """禁用桌面宠物模式"""
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 禁用桌面宠物模式
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = False
        
        # 保存配置
        config_manager.save_config(config)
        
        print("✅ 桌面宠物模式已禁用！")
        print("现在将使用传统GUI界面")
        
    except Exception as e:
        print(f"❌ 禁用失败: {e}")
        import traceback
        traceback.print_exc()

def show_status():
    """显示当前状态"""
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        pet_config = config.get("DESKTOP_PET", {})
        enabled = pet_config.get("enabled", False)
        
        print(f"桌面宠物模式状态: {'✅ 已启用' if enabled else '❌ 已禁用'}")
        
        if enabled:
            print("\n当前配置:")
            print(f"  角色: {pet_config.get('character', 'default')}")
            print(f"  大小缩放: {pet_config.get('size_scale', 1.0)}")
            position = pet_config.get('position', {})
            print(f"  位置: x={position.get('x', -1)}, y={position.get('y', -1)}")
        
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")

def main():
    """主函数"""
    print("=== 桌面宠物配置工具 ===")
    print("1. 启用桌面宠物模式")
    print("2. 禁用桌面宠物模式")
    print("3. 查看当前状态")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == "1":
                enable_desktop_pet()
            elif choice == "2":
                disable_desktop_pet()
            elif choice == "3":
                show_status()
            elif choice == "4":
                print("再见！")
                break
            else:
                print("无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"操作失败: {e}")

if __name__ == "__main__":
    main() 