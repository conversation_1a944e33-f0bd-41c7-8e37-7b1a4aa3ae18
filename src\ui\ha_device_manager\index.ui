<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HomeAssistantDeviceManager</class>
 <widget class="QMainWindow" name="HomeAssistantDeviceManager">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>480</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>480</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Home Assistant设备管理器</string>
  </property>
  <widget class="QWidget" name="central_widget">
   <layout class="QVBoxLayout" name="main_layout">
    <property name="spacing">
     <number>6</number>
    </property>
    <property name="leftMargin">
     <number>12</number>
    </property>
    <property name="topMargin">
     <number>12</number>
    </property>
    <property name="rightMargin">
     <number>12</number>
    </property>
    <property name="bottomMargin">
     <number>12</number>
    </property>
    <item>
     <layout class="QHBoxLayout" name="navigation_layout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QTabBar" name="nav_segment" native="true">
        <property name="minimumSize">
         <size>
          <width>180</width>
          <height>42</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="available_page">
       <layout class="QVBoxLayout" name="available_layout">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QFrame" name="available_card">
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QVBoxLayout" name="available_card_layout">
           <item>
            <layout class="QHBoxLayout" name="filter_layout">
             <item>
              <widget class="QComboBox" name="domain_combo">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>32</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>32</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>选择设备类型</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="search_input">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>32</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>32</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>搜索设备 (名称或ID)</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="refresh_button">
               <property name="minimumSize">
                <size>
                 <width>80</width>
                 <height>32</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>32</height>
                </size>
               </property>
               <property name="text">
                <string>刷新</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTableWidget" name="device_table">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
             <property name="selectionBehavior">
              <enum>QAbstractItemView::SelectRows</enum>
             </property>
             <column>
              <property name="text">
               <string>Prompt</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>设备ID</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>类型</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>状态</string>
              </property>
             </column>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="add_layout">
             <item>
              <widget class="QLineEdit" name="custom_name_input">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>32</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>32</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>自定义Prompt（可选）</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="add_button">
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>32</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>32</height>
                </size>
               </property>
               <property name="text">
                <string>添加选中设备</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="added_page">
       <layout class="QVBoxLayout" name="added_layout">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QFrame" name="added_card">
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QVBoxLayout" name="added_card_layout">
           <item>
            <widget class="QTableWidget" name="added_device_table">
             <property name="selectionMode">
              <enum>QAbstractItemView::SingleSelection</enum>
             </property>
             <property name="selectionBehavior">
              <enum>QAbstractItemView::SelectRows</enum>
             </property>
             <column>
              <property name="text">
               <string>Prompt</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>设备ID</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>操作</string>
              </property>
             </column>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QTabBar</class>
   <extends>QWidget</extends>
   <header>qtabbar.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
