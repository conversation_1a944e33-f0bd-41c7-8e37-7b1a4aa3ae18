#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import ConfigManager

def main():
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 启用桌面宠物模式
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        
        # 保存配置
        config_manager.save_config(config)
        
        print("✅ 桌面宠物模式已启用！")
        
    except Exception as e:
        print(f"❌ 启用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 