{"mcpServers": {"nGw2Uhvrj74nRLmioiZPR": {"isActive": true, "name": "sanhua", "type": "streamableHttp", "description": "三花AI日报", "baseUrl": "https://sanhua.himrr.com/mcp"}, "xpxuYcXPo5hqe21Q1es4f": {"isActive": true, "name": "xhs-mcp", "type": "streamableHttp", "description": "", "baseUrl": "http://localhost:9999/mcp"}, "everything-search": {"isActive": true, "name": "everything-search", "type": "stdio", "registryUrl": "", "command": "uvx", "args": ["mcp-server-everything-search"], "env": {"EVERYTHING_SDK_PATH": "D:\\everything\\Everything64.dll"}}, "yt6uiANQI5R-nLGQPr8gg": {"isActive": false, "name": "xhs-mcp", "type": "stdio", "description": "", "registryUrl": "", "command": "uv", "args": ["--directory", "/Users/<USER>/xhs-mcp", "run", "main.py"], "env": {"XHS_COOKIE": "gid=yYijjJ4J24dYyYijjJ4ySqq0KJli2Y9JWFfvA1f47jYvhv28YSECqC888qq2JWY8fjiYqJ2y; abRequestId=e1abcd3c-2145-55cf-afc7-0e51360938f4; a1=192b261f140h64wf1tdprop1lpmrw9wd210bdyto450000399469; webId=109112509759383ab44a7ce799b0e2d3; webBuild=4.62.3; acw_tc=0a4ade7817464309824107661e11a6ef83ea110cd383a348f56b447e906d1a; web_session=040069b5bd753768182053262d3a4b212fe63c; customer-sso-sid=68c517500866564173503045jdvevde5fbxdwnhw; x-user-id-creator.xiaohongshu.com=64c9367f000000000e024876; customerClientId=365789317100381; access-token-creator.xiaohongshu.com=customer.creator.AT-68c5175008665641690295988l2siwls5u6wyqyh; galaxy_creator_session_id=OigYIGJV7hJMscfhWwRQLHWJkvPbd1xxCnPx; galaxy.creator.beaker.session.id=1746431590579081600991; websectiga=29098a4cf41f76ee3f8db19051aaa60c0fc7c5e305572fec762da32d457d76ae; sec_poison_id=3189bcf5-331c-47f0-b648-c49736edffd4; xsecappid=xhs-pc-web; loadts=1746431836301; unread={%22ub%22:%2267fa3e30000000001c00ff96%22%2C%22ue%22:%2267f5f5aa000000001c02bfbc%22%2C%22uc%22:23}"}}, "_lvtmhzJPlt50sfwJonB6": {"isActive": true, "name": "fetcher-mcp", "type": "stdio", "description": "", "registryUrl": "", "tags": [], "command": "npx", "args": ["-y", "fetcher-mcp"]}, "R_IVD213_HoRb-3qt-Hfl": {"isActive": true, "name": "MCP-timeserver", "type": "stdio", "description": "", "registryUrl": "https://pypi.tuna.tsinghua.edu.cn/simple", "command": "uvx", "args": ["MCP-timeserver"]}, "zzFnSsx2KzHGof9cn1p2n": {"isActive": true, "name": "edgeone-pages-mcp-server", "type": "stdio", "description": "", "registryUrl": "https://registry.npmmirror.com", "command": "npx", "args": ["edgeone-pages-mcp"]}, "bQyYMPmSrL0o7v8nXSrF3": {"isActive": true, "name": "firecrawl-mcp", "type": "stdio", "description": "", "registryUrl": "", "command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-7e3a530a6a1646c988934431decf3382"}}, "J5OUpzYoQLcq4as4flnrc": {"isActive": true, "name": "desktop-commander", "type": "stdio", "description": "", "registryUrl": "", "command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "YXmaEYb4fTAYoQCbK0EIU": {"isActive": false, "name": "三花AI日报", "type": "sse", "description": "三花AI日报", "baseUrl": "https://sanhua.himrr.com/mcp/sse"}, "xtTIBdtoiemrDthzyffD_": {"isActive": true, "name": "高德地图", "type": "sse", "description": "", "baseUrl": "https://mcp.amap.com/sse?key=5d6ce2a24d3bccde9e68a73d16ca210e"}, "Hzh2srAIUHGNToyHjyhX4": {"isActive": true, "name": "mcp-auto-install", "description": "Automatically install MCP services (Beta version)", "baseUrl": "", "command": "npx", "args": ["-y", "@mcpmarket/mcp-auto-install", "connect", "--json"], "registryUrl": "https://registry.npmmirror.com", "env": {}}, "1npjIcYX2fnaXemltDLW5": {"name": "grok2-imag", "type": "stdio", "description": "", "isActive": true, "registryUrl": "", "command": "npx", "args": ["grok2-image-mcp-server"], "env": {"XAIAPI_KEY": "************************************************************************************"}}, "u0_jarsfLDSmdYuOt0hcK": {"isActive": false, "name": "fetch", "command": "uvx", "args": ["mcp-server-fetch"]}}}