#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.display.desktop_pet_display import DesktopPetDisplay

def main():
    """简单测试桌面宠物"""
    print("启动桌面宠物测试...")
    
    # 创建应用
    app = QApplication(sys.argv)
    
    try:
        # 创建桌面宠物显示
        pet = DesktopPetDisplay()
        
        # 启动显示
        pet.start()
        
        print("桌面宠物已启动！")
        print("- 左键拖拽移动位置")
        print("- 右键打开菜单")
        print("- 关闭窗口退出")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 