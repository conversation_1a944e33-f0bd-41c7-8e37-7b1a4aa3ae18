# -*- coding: utf-8 -*-
from mcp.server.fastmcp import FastMCP
import requests
from playsound import playsound
import tempfile
import os
import logging
import threading
import json
import sys
import locale

# 设置默认编码
if sys.platform.startswith('win'):
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
    os.environ['PYTHONIOENCODING'] = 'utf-8'

# 初始化MCP和日志
mcp = FastMCP("MusicPlayer")
logger = logging.getLogger(__name__)

# 配置日志处理
handler = logging.StreamHandler()
handler.setStream(sys.stdout)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

_LOCK = threading.Lock()  # 保留原线程锁

_API_URL = 'https://api.yaohud.cn/api/music/wy'
_API_KEY = 'GKo5uYntPMtRfxGt1ff'

# 设置缓存目录
_CACHE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'cache', 'music')
os.makedirs(_CACHE_DIR, exist_ok=True)
logger.info(f"使用缓存目录: {_CACHE_DIR}")

def _search_with_retry(song_name: str, retries: int = 3) -> tuple[str, str]:
    """
    使用不同的搜索策略重试获取音乐URL
    
    Args:
        song_name: 歌曲名称
        retries: 重试次数
        
    Returns:
        tuple[str, str]: (音乐URL, 实际歌曲名称)
    """
    search_variations = [
        lambda s: s,  # 原始搜索词
        lambda s: f"{s} 原唱",  # 添加原唱标记
        lambda s: s.split()[0] if len(s.split()) > 1 else s,  # 只使用第一个词
    ]
    
    errors = []
    for i in range(retries):
        search_term = search_variations[i % len(search_variations)](song_name)
        logger.info(f"尝试搜索（第{i+1}次）: {search_term}")
        
        try:
            params = {'key': _API_KEY, 'msg': search_term.strip(), 'n': '1'}
            resp = requests.post(_API_URL, params=params, timeout=10)
            resp.raise_for_status()
            
            data = resp.json()
            logger.info(f"API响应数据: {json.dumps(data, ensure_ascii=False)}")
            
            if 'data' in data and 'musicurl' in data['data'] and data['data']['musicurl']:
                actual_name = f"{data['data']['name']} - {data['data']['songname']}"
                return data['data']['musicurl'], actual_name
            
            error_msg = f"尝试 '{search_term}' 未找到可用的音乐URL"
            logger.warning(error_msg)
            errors.append(error_msg)
            
        except Exception as e:
            error_msg = f"搜索 '{search_term}' 时出错: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
    
    # 所有重试都失败
    error_details = "\n".join(f"- {err}" for err in errors)
    raise ValueError(f"无法找到歌曲 '{song_name}'，尝试以下搜索均失败：\n{error_details}")

@mcp.tool(name="search_music", description="搜索并播放指定的音乐")
def search_music(song_name: str) -> str:
    """
    通过MCP接口搜索并播放音乐（线程安全）
    Args:
        song_name: 歌曲名称
    Returns:
        str: 播放结果或错误信息
    """
    logger.info(f"收到音乐搜索请求: {song_name}")
    
    if not song_name.strip():
        return "错误：歌曲名不能为空"

    with _LOCK:
        try:
            # 1. 搜索并获取音乐URL（带重试）
            music_url, actual_name = _search_with_retry(song_name)
            logger.info(f"找到音乐: {actual_name}")
            logger.info(f"音乐URL: {music_url}")

            # 2. 下载音乐文件到缓存目录
            logger.info("开始下载音乐文件...")
            music_response = requests.get(music_url, timeout=10)
            music_response.raise_for_status()
            
            # 3. 保存到缓存目录
            safe_filename = "".join(x for x in actual_name if x.isalnum() or x in (' ', '-', '_')).strip()
            cache_path = os.path.join(_CACHE_DIR, f"{safe_filename}.mp3")
            
            with open(cache_path, 'wb') as f:
                f.write(music_response.content)
            logger.info(f"音乐文件已保存到: {cache_path}")

            # 4. 播放音乐
            logger.info("开始播放音乐...")
            playsound(cache_path)
            logger.info("音乐播放完成")
            
            return f"播放成功: {actual_name}"

        except ValueError as e:
            # 搜索失败的详细错误
            error_msg = str(e)
            logger.error(error_msg)
            return error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"下载失败: {str(e)}"
            logger.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"播放失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

if __name__ == "__main__":
    logger.info("启动MCP音乐播放服务...")
    logger.info("已注册音乐搜索工具 'search_music'")
    mcp.run(transport="stdio")  # MCP标准输入输出模式
