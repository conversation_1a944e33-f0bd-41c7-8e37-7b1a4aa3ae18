# 视觉识别功能

## 功能概述

py-xiaozhi提供了摄像头控制和视觉识别功能，支持通过语音命令打开/关闭摄像头，以及对摄像头捕获的画面进行智能识别分析。

## 配置说明

视觉识别功能需要在配置文件中进行相关设置：

```json
"CAMERA": { 
  "camera_index": 0,         // 摄像头索引，0通常是电脑内置摄像头
  "frame_width": 640,        // 画面宽度
  "frame_height": 480,       // 画面高度
  "fps": 30,                 // 帧率
  "Loacl_VL_url": "https://open.bigmodel.cn/api/paas/v4/", // 智普API地址
  "VLapi_key": "你的key",     // 智普视觉大模型API密钥
  "models": "glm-4v-plus"    // 使用的视觉模型
}
```

## 智普视觉大模型配置

1. 访问 [智普AI开放平台](https://open.bigmodel.cn/)
2. 注册账号并创建API密钥
3. 将获取的API密钥配置到`config.json`的`CAMERA.VLapi_key`字段
4. 可以选择使用的模型，默认为`glm-4v-plus`

## 使用方法

### 语音命令控制

系统支持以下语音命令控制摄像头和视觉识别功能：

- **打开摄像头**：激活系统摄像头，开始捕获视频流
- **关闭摄像头**：停止摄像头捕获
- **识别画面**：对当前摄像头画面进行智能视觉分析，识别画面中的内容
- **分析图像**：对当前画面进行详细视觉分析并提供描述
- **看到了什么**：询问当前摄像头看到的内容

### GUI 界面控制

在图形界面模式下，可以通过界面上的相关按钮控制摄像头功能。

## 内部实现

视觉识别功能通过IoT模块中的CameraVL设备类实现，主要由Camera和VL两个组件组成：

1. **Camera 组件**：负责摄像头的基本控制，如开启、关闭、获取视频帧等
2. **VL（Vision Language）组件**：负责对图像进行智能分析，调用智普视觉大模型API

实现结构：

```
CameraVL                 # 摄像头与视觉识别集成设备
├── Camera.py            # 摄像头控制模块
└── VL.py                # 视觉语言分析模块
```

## 工作流程

1. 用户通过语音命令打开摄像头
2. 系统激活摄像头并在界面显示视频流
3. 用户请求识别当前画面
4. 系统截取当前帧，并将图像发送给智普视觉大模型
5. 获取视觉分析结果并通过语音或文字反馈给用户

## 隐私说明

视觉识别功能会使用您的摄像头并处理画面内容，请注意：

1. 摄像头捕获的画面仅用于本地分析或发送至智普API进行分析
2. 非语音命令控制时，摄像头处于关闭状态
3. 可以在配置中修改摄像头设置或完全禁用此功能

## 常见问题

1. **摄像头无法打开**
   - 确认您的设备有可用摄像头
   - 检查摄像头是否被其他应用占用
   - 确认已授予应用使用摄像头的权限

2. **视觉识别功能无响应**
   - 检查智普API密钥是否正确配置
   - 确认网络连接正常
   - 检查是否超出API调用次数限制

3. **识别结果不准确**
   - 尝试改善摄像头光线条件
   - 确保目标对象在画面中清晰可见
   - 可能需要升级智普视觉模型版本

4. **摄像头画面卡顿**
   - 尝试在配置中降低分辨率或帧率
   - 关闭其他占用系统资源的应用
   - 更新摄像头驱动 