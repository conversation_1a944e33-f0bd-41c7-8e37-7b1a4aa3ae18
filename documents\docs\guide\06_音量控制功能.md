# 音量控制功能

## 功能概述

本应用支持调整系统音量，根据不同操作系统需要安装不同的依赖。应用程序会在启动时自动检查这些依赖是否已安装。如果缺少依赖，将会显示相应的安装指令。

## 平台支持

系统针对不同操作系统提供了不同的音量控制实现：

1. **Windows**: 使用 pycaw 和 comtypes 控制系统音量
2. **macOS**: 使用 applescript 控制系统音量
3. **Linux**: 根据系统环境使用 pactl (PulseAudio)、amixer (ALSA) 或 alsamixer 控制音量

## 依赖安装

### Windows
```bash
pip install pycaw comtypes
```

### macOS
macOS系统需要安装applescript模块：
```bash
pip install applescript
```

### Linux
根据您的音频系统安装以下依赖之一：

```bash
# PulseAudio 工具（推荐）
sudo apt-get install pulseaudio-utils

# 或者 ALSA 工具
sudo apt-get install alsa-utils

# 如果需要使用 alsamixer 方式，还需要安装 expect
sudo apt-get install alsa-utils expect
```

## 使用方法

### GUI模式
- 使用界面上的音量滑块直接调节音量
- 滑块会在移动后300毫秒更新系统音量(防抖设计)
- 可通过语音命令控制音量，如"调高音量"、"把音量调到50%"等

### CLI模式
- 使用 `v <音量值>` 命令调节音量，例如 `v 50` 将音量设置为50%
- 支持的命令：
  - `v <数值>` 设置为指定音量值(0-100)

### 语音控制
通过IoT功能，可以使用语音命令控制音量：
- "把音量调到50%"
- "音量调小一点"
- "音量调大"
- "设置音量为80"

## 架构设计

音量控制功能采用分层设计，包括：

1. **VolumeController类** - 底层实现，负责跨平台音量操作
2. **BaseDisplay.update_volume** - 中间层，应用程序与底层控制器的桥接
3. **Speaker IoT设备** - 高级抽象，提供语音命令接口

## 内部实现

### 1. VolumeController类

VolumeController类是一个跨平台的音量控制实现，支持Windows、macOS和Linux系统：

```python
# src/utils/volume_controller.py
class VolumeController:
    """跨平台音量控制器"""
    
    def __init__(self):
        self.system = platform.system()
        # 根据不同操作系统初始化控制器
        if self.system == "Windows":
            self._init_windows()
        elif self.system == "Darwin":  # macOS
            self._init_macos()
        elif self.system == "Linux":
            self._init_linux()
    
    def get_volume(self):
        """获取当前音量 (0-100)"""
        # 根据不同平台实现获取音量
        
    def set_volume(self, volume):
        """设置音量 (0-100)"""
        # 根据不同平台实现设置音量
```

### 2. BaseDisplay音量控制

BaseDisplay类提供音量控制接口，由CLI和GUI显示类继承：

```python
# src/display/base_display.py
class BaseDisplay(ABC):
    def __init__(self):
        self.current_volume = 70  # 默认音量值
        self.volume_controller = None
        
        # 初始化音量控制器
        try:
            from src.utils.volume_controller import VolumeController
            if VolumeController.check_dependencies():
                self.volume_controller = VolumeController()
                self.current_volume = self.volume_controller.get_volume()
        except Exception as e:
            # 错误处理...
    
    def get_current_volume(self):
        """获取当前音量"""
        if self.volume_controller:
            try:
                self.current_volume = self.volume_controller.get_volume()
            except Exception:
                pass
        return self.current_volume

    def update_volume(self, volume: int):
        """更新系统音量"""
        volume = max(0, min(100, volume))
        self.current_volume = volume
        
        if self.volume_controller:
            try:
                self.volume_controller.set_volume(volume)
            except Exception:
                # 错误处理...
                pass
```

### 3. Speaker IoT设备

Speaker类是一个IoT设备，允许通过语音命令控制音量：

```python
# src/iot/things/speaker.py
from src.application import Application
from src.iot.thing import Thing, Parameter, ValueType

class Speaker(Thing):
    def __init__(self):
        super().__init__("Speaker", "当前 AI 机器人的扬声器")
        
        # 获取当前显示实例的音量作为初始值
        try:
            app = Application.get_instance()
            self.volume = app.display.current_volume
        except Exception:
            # 如果获取失败，使用默认值
            self.volume = 100  # 默认音量

        # 定义音量属性
        self.add_property("volume", "当前音量值", lambda: self.volume)

        # 定义设置音量方法
        self.add_method(
            "SetVolume", 
            "设置音量",
            [Parameter("volume", "0到100之间的整数", ValueType.NUMBER, True)],
            lambda params: self._set_volume(params["volume"].get_value())
        )

    def _set_volume(self, volume):
        """设置音量的具体实现"""
        if 0 <= volume <= 100:
            self.volume = volume
            try:
                app = Application.get_instance()
                app.display.update_volume(volume)
                return {"success": True, "message": f"音量已设置为: {volume}"}
            except Exception as e:
                return {"success": False, "message": f"设置音量失败: {e}"}
        else:
            raise ValueError("音量必须在0-100之间")
```

### 4. 在Application中注册

音量控制设备在应用程序启动时被注册：

```python
# src/application.py (部分代码)
def _initialize_iot_devices(self):
    """初始化物联网设备"""
    from src.iot.thing_manager import ThingManager
    from src.iot.things.speaker import Speaker
    
    # 获取物联网设备管理器实例
    thing_manager = ThingManager.get_instance()

    # 添加音量控制设备
    thing_manager.add_thing(Speaker())
```

## 常见问题

1. **无法调节音量**
   - 检查是否安装了对应操作系统的音量控制依赖
   - Windows用户确保安装了pycaw和comtypes
   - macOS用户确保安装了applescript模块
   - Linux用户确保安装了对应的音频控制工具(pactl或amixer)

2. **调节音量命令无响应**
   - 确保IoT模块正常运行
   - 检查系统音频设备是否正常工作
   - 尝试重启应用

3. **音量调节不准确**
   - 可能是由于不同音频接口导致的精度问题
   - 尝试使用较大幅度的调节命令
   
4. **GUI滑块与实际音量不同步**
   - 在某些情况下，系统音量可能被其他应用程序更改
   - 重新启动应用程序将重新获取当前系统音量 