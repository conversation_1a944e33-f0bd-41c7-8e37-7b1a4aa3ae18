# 桌面宠物功能使用指南

## 功能概述

桌面宠物模式是小智助手的一个新功能，它将传统的GUI界面转换为一个可爱的桌面宠物，可以在桌面上自由移动，并通过动画和气泡显示状态信息。

## 功能特性

### 🎨 视觉特性
- **透明背景**: 无边框窗口，与桌面完美融合
- **动画支持**: 支持GIF动画，包括待机、聊天、思考等状态
- **状态气泡**: 显示当前状态和消息的半透明气泡
- **可拖拽**: 左键拖拽可以移动宠物位置

### 🎯 交互功能
- **右键菜单**: 提供设置、切换界面、退出等选项
- **位置记忆**: 自动保存和恢复宠物位置
- **状态同步**: 与语音助手状态实时同步

### 🔧 配置选项
- **启用/禁用**: 可以随时切换桌面宠物模式
- **角色选择**: 支持不同的角色动画
- **大小调节**: 可调整宠物显示大小
- **位置设置**: 自定义初始位置

## 安装和配置

### 1. 准备动画文件

确保以下动画文件存在：
```
assets/characters/default/
├── idle.gif      # 待机动画
├── listening.gif # 聊天动画  
├── speaking.gif  # 说话动画
└── thinking.gif  # 思考动画
```

### 2. 启用桌面宠物模式

运行配置工具：
```bash
python enable_desktop_pet.py
```

选择选项 1 来启用桌面宠物模式。

### 3. 启动应用

```bash
python main.py --mode gui
```

如果桌面宠物模式已启用，将自动显示桌面宠物而不是传统GUI。

## 使用方法

### 基本操作
- **移动宠物**: 左键按住宠物拖拽到想要的位置
- **打开菜单**: 右键点击宠物显示上下文菜单
- **查看状态**: 状态信息会通过气泡显示

### 菜单选项
- **打开设置**: 打开设置窗口（功能开发中）
- **切换到传统界面**: 切换回传统GUI界面
- **退出**: 关闭应用程序

### 状态显示
宠物会根据不同状态显示相应动画：
- **😊 待机**: 空闲状态，显示idle动画
- **👂 聊天**: 正在聊天，显示listening动画
- **💬 说话**: 正在说话，显示speaking动画
- **🤔 思考**: 正在思考，显示thinking动画

## 配置文件

桌面宠物的配置保存在应用配置中的 `DESKTOP_PET` 部分：

```json
{
  "DESKTOP_PET": {
    "enabled": true,
    "character": "default",
    "size_scale": 1.0,
    "position": {
      "x": 100,
      "y": 100
    },
    "interaction": {
      "sound_effects": true,
      "bubble_display": true
    },
    "animation": {
      "frame_rate": 30,
      "transition_smooth": true
    }
  }
}
```

## 测试工具

### 简单测试
```bash
python simple_test.py
```
直接启动桌面宠物进行测试。

### 完整测试
```bash
python test_desktop_pet.py
```
运行完整的测试套件。

### 配置管理
```bash
python enable_desktop_pet.py
```
管理桌面宠物模式的启用/禁用状态。

## 故障排除

### 常见问题

1. **宠物不显示**
   - 检查是否已启用桌面宠物模式
   - 确认动画文件是否存在
   - 查看控制台错误信息

2. **动画不播放**
   - 检查GIF文件是否有效
   - 确认文件路径是否正确
   - 查看日志中的警告信息

3. **位置不正确**
   - 删除配置文件中的position设置
   - 重启应用让宠物回到默认位置

### 日志查看
桌面宠物的运行日志会显示在控制台中，包括：
- 初始化信息
- 动画加载状态
- 错误和警告信息

## 开发信息

### 文件结构
```
src/display/desktop_pet_display.py  # 桌面宠物显示类
src/application.py                  # 应用主类（已修改支持桌面宠物）
src/utils/config_manager.py         # 配置管理器（已添加桌面宠物配置）
```

### 扩展开发
- 添加新的动画状态
- 自定义角色和动画
- 增加更多交互功能
- 集成语音识别和TTS

## 更新日志

### v1.0.0
- ✅ 基础桌面宠物功能
- ✅ 动画播放支持
- ✅ 拖拽移动功能
- ✅ 右键菜单
- ✅ 位置记忆
- ✅ 状态气泡显示
- ✅ 配置管理工具

### 计划功能
- 🔄 设置窗口
- 🔄 多角色支持
- 🔄 自定义动画
- 🔄 语音交互集成
- 🔄 主题切换

## 技术支持

如果遇到问题或有功能建议，请：
1. 查看控制台日志信息
2. 检查配置文件设置
3. 尝试重启应用
4. 提交问题报告

---

**注意**: 桌面宠物功能目前处于测试阶段，部分功能可能还在完善中。 