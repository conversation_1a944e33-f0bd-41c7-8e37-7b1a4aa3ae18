# 小智AI本地TTS功能说明

## 功能简介

小智AI现已支持本地文字转语音（TTS）功能，允许用户在命令行界面下直接朗读指定文本。该功能基于Pyttsx3，无需联网即可使用，提供高质量的中文语音合成。

## 技术实现

- 使用Pyttsx3引擎生成语音
- 支持命令行下快速触发
- 语音数据会发送到本地音频设备播放
- 可实现纯本地的文本朗读功能

## 使用方法

### 命令行模式下使用

在命令行模式下，使用以下命令触发TTS功能：

```
你想要发送的文本
```

例如：
```
你好，小智
```

执行后，系统会将输入的文本转换为语音并播放。

### 使用技巧

1. **长文本朗读**：支持较长文本的朗读，文本中可以包含标点符号
2. **音量控制**：可以通过`v 数字`命令调整音量，例如：`v 80`
3. **中断朗读**：如需中断当前朗读，可以使用`x`命令
4. **并发控制**：当前朗读未完成时，新的TTS请求会自动排队

## 依赖说明

本功能依赖以下Python库：
- edge-tts：Microsoft Edge TTS引擎的Python接口
- soundfile：音频文件处理
- pydub：音频转换和处理
- numpy：数据处理

## 常见问题

1. **无法播放声音**：
   - 检查系统音频设备是否正常工作
   - 确保音量设置适当（使用`v 80`等命令调整）
   - 验证系统是否已安装必要的音频驱动

2. **TTS生成速度慢**：
   - 首次使用可能需要下载语音模型，会稍慢
   - 确保网络连接正常（Edge TTS需要网络连接）
   - 较长文本处理需要更多时间

3. **声音质量问题**：
   - 默认使用Microsoft Edge TTS中的"zh-CN-XiaoxiaoNeural"女声
   - 如需其他声音，可修改源码中的`voice`参数